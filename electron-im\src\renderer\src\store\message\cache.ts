// 缓存管理模块
import type { Ref } from 'vue'
import { apiClient } from '../../api'
import { dbManager } from '../../db/db-manager'
import type { Message, ChatHistoryResponse, DatabaseQueryOptions } from './types'

export class CacheManager {
  private messages: Ref<Map<string, Message[]>>
  private isLoading: Ref<boolean>
  private error: Ref<string>
  private loadingUsers: Set<string> = new Set() // 按用户ID管理加载状态

  constructor(messages: Ref<Map<string, Message[]>>, isLoading: Ref<boolean>, error: Ref<string>) {
    this.messages = messages
    this.isLoading = isLoading
    this.error = error
  }

  // 初始化数据库
  async initDatabase(getCurrentUserId: () => string) {
    try {
      const currentUser = getCurrentUserId()
      if (!currentUser) {
        throw new Error('用户未登录，无法初始化数据库')
      }

      await dbManager.initialize(currentUser)
    } catch (err) {
      this.error.value = err instanceof Error ? err.message : '数据库初始化失败'
      throw err
    }
  }

  // 获取聊天历史
  async loadChatHistory(
    otherUserId: string,
    page = 1,
    limit = 50,
    updateChatSession: (userId: string, message: Message) => Promise<void>,
    createEmptySession: (userId: string) => Promise<any>
  ): Promise<boolean> {
    // 检查该用户是否正在加载中
    if (this.loadingUsers.has(otherUserId)) {
      return true // 跳过重复请求应该返回 true，表示"成功处理"
    }
    // 标记该用户为加载中
    this.loadingUsers.add(otherUserId)

    // 更新全局加载状态（用于UI显示）
    this.isLoading.value = true
    this.error.value = ''
    try {
      let loadedMessages: Message[] = []
      let fromCache = false

      // 首先检查本地数据库是否有历史消息
      try {
        const hasLocalMessages = await dbManager.hasMessages(otherUserId)
        if (hasLocalMessages) {
          // 从本地数据库加载消息
          try {
            loadedMessages = await this.getMessagesFromDatabase(otherUserId, {
              limit,
              offset: (page - 1) * limit
            })
            fromCache = true
          } catch (dbError) {
            console.error(`🔍 [loadChatHistory] 从数据库加载消息失败:`, dbError)
            // 数据库读取失败，降级到API加载
          }
        }
      } catch (dbCheckError) {
        console.error(`🔍 [loadChatHistory] 检查数据库失败:`, dbCheckError)
        // 数据库检查失败，直接使用API
      }

      // 如果没有从缓存加载到消息，则从API获取
      if (!fromCache) {
        try {
          const response = await this.getMessagesFromAPI(otherUserId, page, limit)
          if (response && response.success) {
            loadedMessages = response.messages
            // 批量存储到数据库（不阻塞主流程）
            if (loadedMessages.length > 0) {
              await this.storeMessagesToDatabase(loadedMessages, otherUserId)
            }
          } else {
            console.error(`获取聊天历史失败 - 用户${otherUserId}, API响应:`, response)
            this.error.value = response?.message || '获取聊天历史失败，请检查网络连接'
            return false
          }
        } catch (apiError) {
          console.error(`🔍 [loadChatHistory] API请求失败:`, apiError)
          this.error.value =
            apiError instanceof Error ? apiError.message : '网络请求失败，请检查网络连接'
          return false
        }
      }

      // 将消息添加到store
      const newMessagesAdded = await this.mergeMessagesToStore(otherUserId, loadedMessages)

      // 更新聊天会话
      if (newMessagesAdded > 0) {
        const allMessages = this.messages.value.get(otherUserId) || []
        const lastMessage = allMessages[allMessages.length - 1]
        if (lastMessage) {
          await updateChatSession(otherUserId, lastMessage)
        }
      } else {
        // 即使没有消息，也要确保创建聊天会话（避免显示"暂无消息"）
        await createEmptySession(otherUserId)
      }

      return true
    } catch (err) {
      console.error(`🔍 [loadChatHistory] 获取用户${otherUserId}的聊天历史失败:`, err)
      this.error.value = err instanceof Error ? err.message : '获取聊天历史失败'
      return false
    } finally {
      // 移除该用户的加载状态
      this.loadingUsers.delete(otherUserId)
      // 如果没有其他用户在加载，则清除全局加载状态
      if (this.loadingUsers.size === 0) {
        this.isLoading.value = false
      }
    }
  }

  // 从数据库获取消息
  private async getMessagesFromDatabase(
    userId: string,
    options: DatabaseQueryOptions
  ): Promise<Message[]> {
    return await dbManager.getMessages(userId, options)
  }

  // 从API获取消息
  private async getMessagesFromAPI(
    otherUserId: string,
    page: number,
    limit: number
  ): Promise<ChatHistoryResponse | null> {
    const response = await apiClient.getChatHistory(otherUserId, page, limit)
    if (response && response.success) {
      // 处理API返回的消息，转换时间戳格式
      const processedMessages = (response.messages || []).map((msg) => ({
        ...msg,
        timestamp:
          typeof msg.timestamp === 'string' ? new Date(msg.timestamp).getTime() : msg.timestamp
      }))

      return {
        ...response,
        messages: processedMessages
      }
    }

    return response
  }

  // 批量存储消息到数据库
  private async storeMessagesToDatabase(messages: Message[], userId: string) {
    try {
      await dbManager.storeMessages(messages, userId)
    } catch (storeError) {
      console.error(`🔍 [storeMessagesToDatabase] 批量存储消息到数据库失败:`, storeError)
      // 存储失败不影响消息显示
    }
  }

  // 将消息合并到store中
  private async mergeMessagesToStore(userId: string, newMessages: Message[]): Promise<number> {
    const existingMessages = this.messages.value.get(userId) || []
    const filteredNewMessages = newMessages.filter(
      (msg) => !existingMessages.some((existing) => existing.id === msg.id)
    )
    if (filteredNewMessages.length > 0) {
      const allMessages = [...filteredNewMessages, ...existingMessages].sort(
        (a, b) => a.timestamp - b.timestamp
      )
      this.messages.value.set(userId, allMessages)
    }

    return filteredNewMessages.length
  }

  // 存储单条消息到数据库
  async storeMessage(message: Message, userId: string) {
    try {
      await dbManager.storeMessage(message, userId)
    } catch (error) {
      console.error(`🔍 [storeMessage] 消息存储到数据库失败: ${message.id}`, error)
      throw error
    }
  }

  // 检查数据库是否有消息
  async hasMessages(userId: string): Promise<boolean> {
    try {
      return await dbManager.hasMessages(userId)
    } catch (error) {
      console.error(`🔍 [hasMessages] 检查数据库消息失败:`, error)
      return false
    }
  }

  // 检查特定用户是否正在加载
  isUserLoading(userId: string): boolean {
    return this.loadingUsers.has(userId)
  }

  // 获取当前正在加载的用户列表
  getLoadingUsers(): string[] {
    return Array.from(this.loadingUsers)
  }

  // 强制停止特定用户的加载状态（用于异常情况）
  forceStopUserLoading(userId: string) {
    if (this.loadingUsers.has(userId)) {
      this.loadingUsers.delete(userId)
      // 如果没有其他用户在加载，则清除全局加载状态
      if (this.loadingUsers.size === 0) {
        this.isLoading.value = false
      }
    }
  }

  // 清除缓存
  clearCache() {
    this.messages.value.clear()
    // 同时清除所有加载状态
    this.loadingUsers.clear()
    this.isLoading.value = false
  }

  // 清除特定用户的缓存
  clearUserCache(userId: string) {
    this.messages.value.delete(userId)
    // 同时清除该用户的加载状态
    this.forceStopUserLoading(userId)
  }
}
